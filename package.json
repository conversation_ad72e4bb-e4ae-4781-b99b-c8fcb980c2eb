{"name": "jc-club-front", "private": true, "version": "0.0.1", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "eslint": "eslint --max-warnings=0", "pre-check": "tsc && npx lint-staged", "postinstall": "husky install"}, "lint-staged": {"src/**/*.{ts,tsx}": ["npm run eslint"]}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}, "dependencies": {"@ant-design/charts": "^1.4.3", "@ant-design/icons": "^5.2.6", "@reduxjs/toolkit": "^1.9.7", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "ahooks": "^3.8.4", "antd": "^5.9.4", "axios": "^1.5.1", "classnames": "^2.5.1", "dayjs": "^1.11.13", "lodash": "^4.17.21", "pubsub-js": "^1.9.4", "react": "^18.1.0", "react-dom": "^18.1.0", "react-pdf": "^9.1.1", "react-redux": "^8.1.3", "react-router-dom": "^6.16.0", "swiper": "^11.0.4", "wangeditor": "^4.7.15"}, "devDependencies": {"@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.27.0", "@typescript-eslint/parser": "^5.27.0", "@vitejs/plugin-react": "^1.3.0", "cz-customizable": "^6.3.0", "eslint": "^8.16.0", "eslint-plugin-react": "^7.30.0", "eslint-plugin-react-hooks": "^4.5.0", "husky": "^8.0.1", "less": "^4.2.0", "less-loader": "^11.1.3", "postcss": "^8.4.31", "postcss-preset-env": "^9.1.4", "prettier": "^3.0.3", "stylelint": "^14.9.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recess-order": "^3.0.0", "stylelint-config-standard": "^26.0.0", "stylelint-order": "^5.0.0", "typescript": "^4.6.3", "vite": "^4.5.0"}}