import React, { useState } from 'react'
import { UserOutlined } from '@ant-design/icons'
import { DEFAULT_AVATAR } from '@constants/index'
import './index.less'

interface AvatarProps {
  src?: string
  size?: number
  className?: string
  style?: React.CSSProperties
  showIcon?: boolean // 是否在没有头像时显示图标
  iconStyle?: React.CSSProperties // 图标样式
  onClick?: () => void
}

const Avatar: React.FC<AvatarProps> = ({
  src,
  size = 36,
  className = '',
  style = {},
  showIcon = true,
  iconStyle = {},
  onClick
}) => {
  const [imageError, setImageError] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  // 判断是否应该显示图标而不是图片
  const shouldShowIcon = (!src || imageError) && showIcon

  const handleImageError = () => {
    setImageError(true)
  }

  const handleImageLoad = () => {
    setImageLoaded(true)
    setImageError(false)
  }

  // 如果没有头像且设置显示图标，则显示图标
  if (shouldShowIcon) {
    return (
      <div
        className={`avatar-container avatar-icon ${className}`}
        style={{
          width: size,
          height: size,
          borderRadius: '50%',
          backgroundColor: '#1890ff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#fff',
          fontSize: Math.max(size * 0.4, 14),
          cursor: onClick ? 'pointer' : 'default',
          ...style
        }}
        onClick={onClick}
      >
        <UserOutlined style={iconStyle} />
      </div>
    )
  }

  // 显示图片（包括默认头像图片）
  return (
    <img
      className={`avatar-container avatar-image ${className}`}
      src={src || DEFAULT_AVATAR}
      alt="用户头像"
      style={{
        width: size,
        height: size,
        borderRadius: '50%',
        objectFit: 'cover',
        cursor: onClick ? 'pointer' : 'default',
        ...style
      }}
      onError={handleImageError}
      onLoad={handleImageLoad}
      onClick={onClick}
    />
  )
}

export default Avatar
