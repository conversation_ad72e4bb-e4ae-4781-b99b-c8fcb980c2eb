<!DOCTYPE html>
<html>
<head>
    <title>头像功能测试</title>
</head>
<body>
    <h1>默认头像功能测试</h1>
    <p>请在浏览器中访问 http://localhost:81 来测试默认头像功能</p>
    
    <h2>测试场景：</h2>
    <ul>
        <li>1. 登录后查看头部导航的头像显示</li>
        <li>2. 进入用户信息页面查看头像显示</li>
        <li>3. 在圈子功能中查看评论区头像</li>
        <li>4. 在排行榜中查看头像显示</li>
    </ul>
    
    <h2>预期结果：</h2>
    <ul>
        <li>当用户头像为null或空时，应该显示默认头像图片</li>
        <li>所有头像显示位置都应该统一使用默认头像</li>
        <li>头像加载失败时应该fallback到默认头像</li>
    </ul>
</body>
</html>
