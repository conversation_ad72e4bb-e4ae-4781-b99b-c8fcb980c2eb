import { Button, Input, Upload, Image, message } from 'antd'
import { useState, useEffect, FC } from 'react'
import { useSelector } from 'react-redux'
import { commentSave, getCommentList } from '../../service'
import { CommentOutlined, FileImageOutlined, PlusOutlined, SmileOutlined } from '@ant-design/icons'
import Avatar from '@components/avatar'
import './index.less'
import EmojiPicker from '../emoji-picker'

const CommentInput = ({ momentId, getList, replyType, targetId = '', changeActive = null }) => {
  const [comment, setComment] = useState<string>('')
  const { userInfo } = useSelector(store => store.userInfo)
  const userInfoStorage = localStorage.getItem('userInfo')
  const { tokenValue = '' } = userInfoStorage ? JSON.parse(userInfoStorage) : {}
  const [imgList, setImgList] = useState([])

  const changeComment = e => {
    setComment(e.target.value)
  }

  const saveComment = () => {
    const params = {
      momentId,
      replyType,
      content: comment,
      targetId
    }
    if (imgList.length) {
      params.picUrlList = imgList.map(item => item.response.data)
    }
    commentSave(params).then(() => {
      setComment('')
      setImgList([])
      getList()
      changeActive?.(false)
    })
  }

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type='button'>
      <PlusOutlined />
    </button>
  )
  const handleChange = ({ fileList }) => {
    console.log('评论图片上传状态变化:', fileList)

    // 检查上传状态并处理错误
    const updatedFileList = fileList.map(file => {
      if (file.status === 'error') {
        message.error(`文件 ${file.name} 上传失败，请重试`)
      } else if (file.status === 'done' && file.response) {
        console.log('评论图片上传完成，响应数据:', file.response)

        // 处理不同的响应格式
        let imageUrl = null

        if (file.response?.success && file.response?.data) {
          // 标准格式: { success: true, data: "url" }
          imageUrl = file.response.data
        } else if (typeof file.response === 'string') {
          // 直接返回URL字符串
          imageUrl = file.response
        } else if (file.response?.url) {
          // 格式: { url: "url" }
          imageUrl = file.response.url
        } else if (file.response?.data?.url) {
          // 格式: { data: { url: "url" } }
          imageUrl = file.response.data.url
        }

        if (imageUrl) {
          // 更新文件对象，确保有正确的URL
          file.url = imageUrl
          file.thumbUrl = imageUrl
          console.log('评论图片URL设置为:', imageUrl)
        } else {
          console.error('无法从响应中提取图片URL:', file.response)
          message.error(file.response?.message || '图片上传失败：无法获取图片URL')
        }
      }
      return file
    })
    setImgList(updatedFileList)
  }
  return (
    <div className='comment-wrapper'>
      <Avatar src={userInfo?.avatar} size={40} className='avatar' showIcon={true} />
      <div className='text-area-outer-box'>
        <div className='text-area-box'>
          <Input.TextArea
            onChange={changeComment}
            placeholder='和平发言'
            style={{ border: 'none', paddingLeft: 0 }}
            maxLength={1000}
            value={comment}
          />
          <Upload
            name='uploadFile'
            action='/oss/upload'
            listType='picture-card'
            fileList={imgList}
            withCredentials
            headers={{
              token: 'yt ' + tokenValue
            }}
            data={{
              bucket: 'javaloser',
              objectName: 'icon'
            }}
            onChange={handleChange}
          >
            {imgList.length >= 8 || imgList.length === 0 ? null : uploadButton}
          </Upload>
        </div>
        <div className='comment-bottom'>
          <div className='icon-box flex'>
            <div style={{ marginRight: 20, cursor: 'pointer' }}>
              <EmojiPicker onChoose={txt => setComment(pre => pre + txt)}>
                <SmileOutlined />
              </EmojiPicker>
            </div>
            <div style={{ cursor: 'pointer' }}>
              <Upload
                name='uploadFile'
                className='avatar-uploader'
                accept='image/*'
                showUploadList={false}
                withCredentials
                action='/oss/upload'
                headers={{
                  token: 'yt ' + tokenValue
                }}
                data={{
                  bucket: 'javaloser',
                  objectName: 'icon'
                }}
                onChange={handleChange}
              >
                <div>
                  <FileImageOutlined />
                  {/* <span style={{ marginLeft: '8px' }}>图片</span> */}
                </div>
              </Upload>
              {/* <FileImageOutlined /> */}
            </div>
          </div>
          <div className='submit-btn-box flex'>
            <div className='text-num-box' style={{ marginRight: 20 }}>
              {comment.length}/1000
            </div>
            <Button onClick={saveComment} disabled={comment.length === 0} type='primary'>
              回复
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

function formatDistanceToNow(date) {
  if (!date) return
  const delta = Math.abs(Date.now() - date)

  if (delta < 30 * 1000) {
    return '刚刚'
  } else if (delta < 5 * 60 * 1000) {
    return Math.round(delta / 1000) + '秒前'
  } else if (delta < 60 * 60 * 1000) {
    return Math.round(delta / 60000) + '分钟前'
  } else if (delta < 24 * 60 * 60 * 1000) {
    return Math.round(delta / 3600000) + '小时前'
  } else if (delta < 7 * 24 * 60 * 60 * 1000) {
    return Math.round(delta / 86400000) + '天前'
  } else {
    return new Date(date).toLocaleDateString()
    return '很久之前'
  }
}

const CommentItem = ({
  momentId,
  id,
  avatar,
  userName,
  content,
  createdTime,
  replyType,
  getList,
  children,
  picUrlList,
  toName
}) => {
  const [active, setActive] = useState(false)
  const toggleActive = () => {
    setActive(!active)
  }
  return (
    <div className={`comment-item-wrapper`}>
      <div className='flex align-top'>
        <Avatar src={avatar} size={40} className='avatar' showIcon={true} />
        <div className='comment-detail-wrapper'>
          {replyType === 1 ? (
            <div className='title'>{userName}</div>
          ) : (
            <div className='title'>
              {userName} 回复 {toName}：
            </div>
          )}

          <div className='comment-content'>{content}</div>
          {picUrlList?.length && (
            <Image.PreviewGroup items={picUrlList}>
              <div className='comment-img-list'>
                {picUrlList.map((t: string) => (
                  <Image key={t} width={90} src={t} />
                ))}
              </div>
            </Image.PreviewGroup>
          )}
          <div className='comment-bottom-wrapper flex'>
            <div>{formatDistanceToNow(createdTime) || '12小时前'}</div>
            <div onClick={toggleActive} className={`bottom-btn ${active ? 'active' : ''}`}>
              <CommentOutlined />
              <span style={{ marginLeft: 5 }}>
                {active ? '取消回复' : replyType === 1 ? '评论' : '回复'}
              </span>
            </div>
          </div>
          {active && (
            <CommentInput
              momentId={momentId}
              getList={getList}
              targetId={id}
              replyType={2}
              changeActive={setActive}
            />
          )}
          {children?.length
            ? children?.map(item => {
                return <CommentItem key={item.id} {...item} getList={getList} />
              })
            : ''}
        </div>
      </div>
    </div>
  )
}

function flattenNestedObjects(items) {
  const result = []

  function traverse(items) {
    items.forEach(item => {
      // 创建一个新对象来存储当前项的属性（除了 children）
      const flatItem = {}
      for (const key in item) {
        if (key !== 'children') {
          flatItem[key] = item[key]
        }
      }
      // 将扁平化的对象添加到结果数组中
      result.push(flatItem)

      // 如果还有 children，则递归调用 traverse
      if (item.children) {
        traverse(item.children)
      }
    })
  }

  // 从顶层对象开始遍历
  traverse(items)

  return result
}

const CommentList: FC<any> = props => {
  const { momentId, replyCount } = props
  const [replyList, setReplyList] = useState<any[]>([])
  const getList = async () => {
    const res = await getCommentList({ id: momentId })
    if (res.success && res.data) {
      const data = res.data.map(item => {
        return {
          ...item,
          children: flattenNestedObjects(item.children || [])
        }
      })
      console.log(data, 'data')
      setReplyList(data)
    } else {
      setReplyList([])
    }
  }
  useEffect(() => {
    getList()
  }, [])

  return (
    <div className='comment-list-box'>
      <div className='top-arrow'></div>
      <div className='comment-number'>评论 {replyCount}</div>
      <CommentInput momentId={momentId} getList={getList} targetId={momentId} replyType={1} />
      <div className='comment-list-wrapper'>
        {replyList.map((item: Record<string, any>) => {
          return <CommentItem key={item.id} momentId={momentId} getList={getList} {...item} />
        })}
      </div>
    </div>
  )
}
export default CommentList
