import { Button, Card, Input, Upload, message } from 'antd'
import { useState } from 'react'
import './index.less'

const UploadTest = () => {
  const [token, setToken] = useState('')
  const [testResults, setTestResults] = useState<Record<string, string>>({})

  // 检查localStorage中的token格式
  const checkStoredToken = () => {
    const userInfoStorage = localStorage.getItem('userInfo')
    if (userInfoStorage) {
      const userInfo = JSON.parse(userInfoStorage)
      const tokenInfo = {
        'localStorage userInfo': userInfo,
        'tokenValue': userInfo.tokenValue,
        'tokenName': userInfo.tokenName,
        '当前代码会生成的header': userInfo.tokenValue ? 'yt ' + userInfo.tokenValue : '无token'
      }
      showResult('tokenCheck', `Token检查结果:\n${JSON.stringify(tokenInfo, null, 2)}`)
    } else {
      showResult('tokenCheck', 'localStorage中没有找到userInfo')
    }
  }

  const showResult = (key: string, result: string) => {
    setTestResults(prev => ({ ...prev, [key]: result }))
  }

  // 测试后端连接
  const testBackend = async () => {
    try {
      const response = await fetch('http://localhost:8080/oss/upload', {
        method: 'POST',
        mode: 'cors'
      })
      showResult('backend', `直接请求后端:\n状态: ${response.status}\n响应: ${response.statusText}`)
    } catch (error: any) {
      showResult('backend', `直接请求后端失败:\n错误: ${error.message}`)
    }
  }

  // 测试不同路径
  const testPath = async (path: string) => {
    try {
      const response = await fetch(path, {
        method: 'POST'
      })
      const text = await response.text()
      showResult('proxy', `测试路径 ${path}:\n状态: ${response.status}\n响应: ${response.statusText}\n内容: ${text.substring(0, 200)}`)
    } catch (error: any) {
      showResult('proxy', `测试路径 ${path} 失败:\n错误: ${error.message}`)
    }
  }

  // 文件上传测试
  const handleUpload = async (info: any) => {
    const { file } = info
    
    if (file.status === 'uploading') {
      console.log('上传中...', file.name)
      return
    }
    
    if (file.status === 'done') {
      console.log('上传完成:', file.response)
      showResult('upload', `上传成功:\n响应: ${JSON.stringify(file.response, null, 2)}`)
      message.success('上传成功')
    } else if (file.status === 'error') {
      console.log('上传失败:', file.error)
      showResult('upload', `上传失败:\n错误: ${file.error?.message || '未知错误'}`)
      message.error('上传失败')
    }
  }

  // 手动上传测试
  const manualUpload = async (file: File) => {
    const formData = new FormData()
    formData.append('uploadFile', file)
    formData.append('bucket', 'javaloser')
    formData.append('objectName', 'icon')

    const headers: Record<string, string> = {}
    if (token) {
      headers['token'] = 'yt ' + token
    }

    try {
      console.log('发送上传请求到: /oss/upload')
      console.log('请求头:', headers)
      console.log('文件:', file.name)
      
      const response = await fetch('/oss/upload', {
        method: 'POST',
        headers: headers,
        body: formData,
        credentials: 'include'
      })

      const responseText = await response.text()
      showResult('manual', `手动上传结果:\n状态: ${response.status}\n响应: ${responseText}`)
      
      console.log('响应状态:', response.status)
      console.log('响应内容:', responseText)
      
    } catch (error: any) {
      showResult('manual', `手动上传失败:\n错误: ${error.message}`)
      console.error('上传错误:', error)
    }
  }

  return (
    <div className="upload-test-container">
      <h1>上传接口测试工具</h1>
      
      <Card title="配置" style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 16 }}>
          <Button onClick={checkStoredToken} type="default" style={{ marginBottom: 8 }}>
            检查localStorage中的Token格式
          </Button>
          {testResults.tokenCheck && (
            <pre className="test-result">{testResults.tokenCheck}</pre>
          )}
        </div>
        <Input
          placeholder="输入Token"
          value={token}
          onChange={(e) => setToken(e.target.value)}
          style={{ width: 300 }}
        />
      </Card>

      <Card title="1. 测试后端连接" style={{ marginBottom: 16 }}>
        <Button onClick={testBackend} type="primary">
          测试 localhost:8080
        </Button>
        {testResults.backend && (
          <pre className="test-result">{testResults.backend}</pre>
        )}
      </Card>

      <Card title="2. 测试不同接口路径" style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 8 }}>
          <Button onClick={() => testPath('/oss/upload')} style={{ marginRight: 8 }}>
            测试 /oss/upload
          </Button>
          <Button onClick={() => testPath('/upload')} style={{ marginRight: 8 }}>
            测试 /upload
          </Button>
          <Button onClick={() => testPath('/api/upload')} style={{ marginRight: 8 }}>
            测试 /api/upload
          </Button>
          <Button onClick={() => testPath('/file/upload')}>
            测试 /file/upload
          </Button>
        </div>
        {testResults.proxy && (
          <pre className="test-result">{testResults.proxy}</pre>
        )}
      </Card>

      <Card title="3. Ant Design Upload 组件测试" style={{ marginBottom: 16 }}>
        <Upload
          name="uploadFile"
          action="/oss/upload"
          headers={token ? { token: 'yt ' + token } : {}}
          data={{
            bucket: 'javaloser',
            objectName: 'icon'
          }}
          onChange={handleUpload}
          accept="image/*"
        >
          <Button>选择文件上传 (Ant Design)</Button>
        </Upload>
        {testResults.upload && (
          <pre className="test-result">{testResults.upload}</pre>
        )}
      </Card>

      <Card title="4. 手动上传测试">
        <input
          type="file"
          accept="image/*"
          onChange={(e) => {
            const file = e.target.files?.[0]
            if (file) {
              manualUpload(file)
            }
          }}
        />
        {testResults.manual && (
          <pre className="test-result">{testResults.manual}</pre>
        )}
      </Card>
    </div>
  )
}

export default UploadTest
