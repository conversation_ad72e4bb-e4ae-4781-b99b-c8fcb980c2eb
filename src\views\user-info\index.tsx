import { LoadingOutlined, PlusOutlined } from '@ant-design/icons'
import { saveUserInfo } from '@features/userInfoSlice.ts'
import Avatar from '@components/avatar'
import { DEFAULT_AVATAR } from '@constants/index'
import req from '@utils/request'
import { Button, Card, Col, Form, Input, Radio, Row, Upload, message } from 'antd'
import { memo, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'

import './index.less'

const { TextArea } = Input
const apiName = {
  update: '/user/update',
  queryInfo: '/user/getUserInfo'
}

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 10, offset: 1 }
}

interface UserInfo {
  nickName?: string
  phone?: string
  email?: string
  sex?: string | number
  introduce?: string
  avatar?: string
}

const Sex: Record<string, any> = {
  1: '男',
  2: '女'
}

const normFile = (e: any) => {
  if (Array.isArray(e)) {
    return e
  }
  return e?.fileList
}

const UserInfo = () => {
  const userInfoStorage = localStorage.getItem('userInfo')
  const { loginId = '', tokenValue = '' } = userInfoStorage ? JSON.parse(userInfoStorage) : {}

  const dispatch = useDispatch()

  const [form] = Form.useForm()
  const [editFlag, setEditFlag] = useState(false)
  const [loading, setLoading] = useState(false)
  const [userInfo, setUserInfo] = useState<UserInfo>({})
  const [avatar, setAvatar] = useState()

  const getUserInfo = async () => {
    req(
      {
        method: 'post',
        url: apiName.queryInfo,
        data: {
          userName: loginId
        }
      },
      '/auth'
    ).then(res => {
      if (res?.success && res?.data) {
        // 如果用户没有头像，设置默认头像
        const userInfoWithAvatar = {
          ...res.data,
          avatar: res.data.avatar || DEFAULT_AVATAR
        }
        setUserInfo(userInfoWithAvatar)
        setAvatar(userInfoWithAvatar.avatar)
        form.setFieldsValue(userInfoWithAvatar)
      }
    })
  }

  useEffect(() => {
    if (loginId) {
      getUserInfo()
    }
  }, [loginId])

  const onFinish = () => {
    setLoading(true)
    const values = form.getFieldsValue()
    if (!Object.values(values).filter(Boolean).length && !avatar) {
      setLoading(false)
      return
    }
    const params = {
      userName: loginId,
      ...values,
      // 确保头像字段不为空，如果为空则使用默认头像
      avatar: avatar || DEFAULT_AVATAR
    }
    req(
      {
        method: 'post',
        url: apiName.update,
        data: { ...params }
      },
      '/auth'
    )
      .then(res => {
        // 确保保存到Redux的用户信息包含头像
        const updatedUserInfo = {
          ...params,
          avatar: params.avatar || DEFAULT_AVATAR
        }
        dispatch(saveUserInfo(updatedUserInfo))
        setUserInfo(updatedUserInfo)
        setAvatar(updatedUserInfo.avatar)
        if (res.success) {
          message.success('更新成功')
          setTimeout(() => {
            // getUserInfo()
            setLoading(false)
            setEditFlag(false)
          }, 500)
        } else {
          setLoading(false)
        }
      })
      .catch(() => {
        message.error('更新失败')
        setLoading(false)
      })
  }

  const handleChange = ({ file }) => {
    console.log('上传状态变化:', file.status, file.response)

    if (file.status === 'error') {
      message.error(`文件 ${file.name} 上传失败，请重试`)
      return
    }

    if (file.status === 'done') {
      console.log('=== 文件上传完成 ===')
      console.log('完整响应数据:', JSON.stringify(file.response, null, 2))
      console.log('响应数据类型:', typeof file.response)
      console.log('响应数据结构:', file.response)

      // 处理不同的响应格式
      let imageUrl = null

      if (file.response?.success && file.response?.data) {
        // 标准格式: { success: true, data: "url" }
        imageUrl = file.response.data
        console.log('使用标准格式，URL:', imageUrl)
      } else if (typeof file.response === 'string') {
        // 直接返回URL字符串
        imageUrl = file.response
        console.log('使用字符串格式，URL:', imageUrl)
      } else if (file.response?.url) {
        // 格式: { url: "url" }
        imageUrl = file.response.url
        console.log('使用url字段格式，URL:', imageUrl)
      } else if (file.response?.data?.url) {
        // 格式: { data: { url: "url" } }
        imageUrl = file.response.data.url
        console.log('使用嵌套url字段格式，URL:', imageUrl)
      }

      if (imageUrl) {
        setAvatar(imageUrl)
        message.success('头像上传成功')
        console.log('头像URL设置为:', imageUrl)
      } else {
        console.error('=== 无法提取图片URL ===')
        console.error('响应数据:', file.response)
        message.error(file.response?.message || '头像上传失败：无法获取图片URL')
      }
    }
  }

  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>点击上传</div>
    </div>
  )

  return (
    <div className='user-info-box'>
      <Card title='基本信息'>
        <Form {...layout} colon={false} form={form}>
          <Row>
            <Col span={16}>
              {editFlag ? (
                <Form.Item label='用户头像' valuePropName='fileList' getValueFromEvent={normFile}>
                  <Upload
                    name='uploadFile'
                    listType='picture-card'
                    className='avatar-uploader'
                    accept='image/*'
                    showUploadList={false}
                    withCredentials
                    action='/oss/upload'
                    headers={{
                      token: 'yt ' + tokenValue
                    }}
                    data={{
                      bucket: 'javaloser',
                      objectName: 'icon'
                    }}
                    // beforeUpload={beforeUpload}
                    onChange={handleChange}
                  >
                    {avatar && avatar !== DEFAULT_AVATAR ? (
                      <img src={avatar} style={{ height: '80px', width: '80px', borderRadius: '50%', objectFit: 'cover' }} />
                    ) : (
                      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <Avatar src={avatar} size={80} showIcon={false} />
                        <div style={{ marginTop: '8px', fontSize: '12px', color: '#999' }}>
                          点击上传头像
                        </div>
                      </div>
                    )}
                  </Upload>
                </Form.Item>
              ) : (
                <Form.Item label='用户头像'>
                  <Avatar
                    src={userInfo.avatar}
                    size={80}
                    className='user-info_header'
                    showIcon={true}
                  />
                </Form.Item>
              )}
            </Col>
            <Col span={16}>
              {editFlag ? (
                <Form.Item label='用户昵称' name='nickName'>
                  <Input placeholder='请输入昵称' />
                </Form.Item>
              ) : (
                <Form.Item label='用户昵称'>
                  <>{userInfo.nickName || ''}</>
                </Form.Item>
              )}
            </Col>
            <Col span={16}>
              {editFlag ? (
                <Form.Item label='性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别' name='sex'>
                  <Radio.Group>
                    <Radio value={1}>男</Radio>
                    <Radio value={2}>女</Radio>
                  </Radio.Group>
                </Form.Item>
              ) : (
                <Form.Item label='性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别'>
                  <>{userInfo.sex ? Sex[userInfo.sex] : '未知'}</>
                </Form.Item>
              )}
            </Col>
            <Col span={16}>
              {editFlag ? (
                <Form.Item label='手机号码' name='phone'>
                  <Input placeholder='请输入手机号码' />
                </Form.Item>
              ) : (
                <Form.Item label='手机号码'>
                  <>{userInfo.phone || ''}</>
                </Form.Item>
              )}
            </Col>
            <Col span={16}>
              {editFlag ? (
                <Form.Item
                  label='邮&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;箱'
                  name='email'
                >
                  <Input placeholder='请输入邮箱' />
                </Form.Item>
              ) : (
                <Form.Item label='邮&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;箱'>
                  <>{userInfo.email || ''}</>
                </Form.Item>
              )}
            </Col>
            <Col span={16}>
              {editFlag ? (
                <Form.Item label='个人简介' name='introduce'>
                  <TextArea placeholder='请输入个人简介' maxLength={500} showCount />
                </Form.Item>
              ) : (
                <Form.Item label='个人简介'>
                  <>{userInfo.introduce || '这个人很懒，什么也没有留下。。。。'}</>
                </Form.Item>
              )}
            </Col>

            <Col span={16}>
              <Form.Item wrapperCol={{ offset: 5 }}>
                {editFlag ? (
                  <>
                    <Button
                      type='primary'
                      style={{ marginRight: '20px' }}
                      onClick={onFinish}
                      loading={loading}
                    >
                      保存
                    </Button>
                    <Button onClick={() => setEditFlag(false)}>取消</Button>
                  </>
                ) : (
                  <Button type='primary' onClick={() => setEditFlag(true)}>
                    编辑
                  </Button>
                )}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  )
}

export default memo(UserInfo)
